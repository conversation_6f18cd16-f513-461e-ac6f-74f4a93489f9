server:
  port: 8080
spring:
  datasource:
    url: jdbc:postgresql://************:5432/fone-ai-test
    username: postgres
    password: 111111
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 60MB
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 10000
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
  ai:
    retry:
      max-attempts: 5  # 增加最大重试次数
      initial-interval: 2000  # 初始重试间隔(毫秒)
      multiplier: 2.0  # 重试间隔乘数
      max-interval: 30000  # 最大重试间隔(毫秒)
    openai:
      connect-timeout: 60000  # 连接超时时间(毫秒)
      read-timeout: 60000  # 读取超时时间(毫秒)
      # 阿里云
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: sk-3311d9e9f5854030831b6ac64bda5720
      chat:
        options:
          # 阿里云
          model: "qwen-plus-latest"
          simple-model: "qwen-turbo"
          intent-model: "tongyi-intent-detect-v3"
          vl-model: "qwen-vl-plus"
      embedding:
        options:
          model: "text-embedding-v4"
          dimensions: 1536
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: "never"
    vectorstore:
      pgvector:
        index-type: HNSW
        distance-type: COSINE_DISTANCE
        dimensions: 1536
        max-document-batch-size: 10 # Optional: Maximum number of documents per batch
        initialize-schema: true
mybatis-plus:
  mapperLocations: classpath:mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.facilityone.ai.entity.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
app:
  file:
    upload-path: upload
    temp-path: temp

fm:
  base:
    multi-tenant:
      default:
        baseUrl: http://************:23456/f1-shang
        bearerToken: ba356673-b890-49f4-8b27-49ff09fcc926
        type: FM2_0
        toolType: DB
        dbUrl: ******************************************************************************************************************************************************************************************
        dbUserName: root
        dbPassword: 111111
      hl: #恒隆
        baseUrl: https://hl-coe-demo.wuxi.fmone.cn/f1-shang
        bearerToken: ba356673-b890-49f4-8b27-49ff09fcc926
        type: FM2_0
        toolType: API
      ydhx: #远东宏信
        baseUrl: https://fehorizon-fm-demo.wuxi.fmone.cn
        bearerToken: ba356673-b890-49f4-8b27-49ff09fcc926
        type: SHANG
        toolType: API
      dfdxc: #东方大学城
        baseUrl: https://souc-fm-demo.wuxi.fmone.cn
        bearerToken: ba356673-b890-49f4-8b27-49ff09fcc926
        type: SHANG
        toolType: API
      citic: #中信
        baseUrl: https://zhongguozun-fm-demo.wuxi.fmone.cn
        bearerToken: ba356673-b890-49f4-8b27-49ff09fcc926
        type: SHANG
        toolType: API
      ditie14: #地铁14号线
        type: FM2_0
        toolType: DB
        dbUrl: ********************************************************************************************************************************************
        dbUserName: postgres
        dbPassword: postgres
      kuaishou: #快手
        type: SHANG
        toolType: DB
        dbUrl: ********************************************************************************************************************************************************
        dbUserName: root
        dbPassword: Fmone@2025
admin:
  default:
    username: admin
    password: 111111
  jwt:
    secret: FC4z2niibVykfudMVmtQyQldVqn+0YqYvDRw4SqZPYM=
    expiration: 86400000  # 24 hours in milliseconds
