package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.constant.CommonConstant;
import cn.facilityone.ai.dto.FmProjectDTO;
import cn.facilityone.ai.dto.FmUserDataDTO;
import cn.facilityone.ai.dto.FmWechatUserInfoDTO;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.FmContentInfo;
import cn.facilityone.ai.properties.FmProperties;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:17
 */

@Slf4j
@Service
public class FmBaseTool {

    @Autowired
    private DbTool dbTool;

    /**
     * 获取用户信息及有权限的项目
     *
     * @param contentInfo 上下文信息
     * @return 用户信息及有权限的项目
     */
    @LogParam(printReturn = true)
    public FmUserDataDTO getTenantUserInfo(ContentInfo contentInfo, FmProperties.TenantConfig tenantConfig) {
        // TODO 2025/8/6 临时放通demo演示环境，未配置或space环境写死用户名及租户信息
        if ("fm-mix-coe-web-demo".equals(contentInfo.getAppCode()) && (CommonConstant.DEFAULT_TENANT_ID.equals(contentInfo.getTenantId())
         || FmProperties.TenantConfig.Type.SPACE.toString().equals(tenantConfig.getType()))) {
            FmUserDataDTO userDataDTO = new FmUserDataDTO();
            userDataDTO.setId(1L);
            userDataDTO.setUserName(contentInfo.getUsername());
            userDataDTO.setRealName(contentInfo.getUsername());
            userDataDTO.setProjects(List.of(new FmProjectDTO(1L, "demo")));
            return userDataDTO;
        } else {
            if (FmProperties.TenantConfig.ToolType.API.toString().equals(tenantConfig.getToolType())) {
                return getTenantUserInfoFromApi(contentInfo, tenantConfig);
            } else if (FmProperties.TenantConfig.ToolType.DB.toString().equals(tenantConfig.getToolType())) {
                return getTenantUserInfoFromDb(contentInfo, tenantConfig);
            } else {
                return null;
            }
        }
    }


    private FmUserDataDTO getTenantUserInfoFromApi(ContentInfo contentInfo, FmProperties.TenantConfig tenantConfig) {
        String url = tenantConfig.getBaseUrl() + "/ai/base/getUserInfo/" + contentInfo.getUsername();
        String s = tenantFmHttpUtil(url, null, Method.GET, tenantConfig.getBearerToken());
        if (StrUtil.isNotBlank(s)) {
            JSONObject jsonObject = JSONUtil.parseObj(s);
            if (jsonObject.getInt("code") == 200) {
                return JSONUtil.toBean(jsonObject.getJSONObject("data"), FmUserDataDTO.class);
            }
        }
        return null;
    }

    private FmUserDataDTO getTenantUserInfoFromDb(ContentInfo contentInfo, FmProperties.TenantConfig tenantConfig) {
        // 获取用户及项目信息
        String userSql = null;
        if (FmProperties.TenantConfig.Type.FM2_0.toString().equals(tenantConfig.getType())) {
            userSql = StrUtil.format("select id,login_code,real_name,telephone,project from sys_user where login_code like '{}' AND deleted = false", contentInfo.getUsername());
        } else if (FmProperties.TenantConfig.Type.SHANG.toString().equals(tenantConfig.getType())) {
            userSql = StrUtil.format("select id,user_name,real_name,phone,proj_ids from sys_user where user_name like '{}' AND deleted = false", contentInfo.getUsername());
        }
        if (StrUtil.isBlank(userSql)) {
            return null;
        }
        DbTool.Request request = new DbTool.Request(tenantConfig.getDbUrl(), tenantConfig.getDbUserName(), tenantConfig.getDbPassword(), userSql, 1);
        DbTool.Response response = dbTool.apply(request);
        if (response.success() && !response.data().isEmpty()) {
            List<Map<String, Object>> data = response.data();
            Map<String, Object> first = data.getFirst();
            FmUserDataDTO userDataDTO = new FmUserDataDTO();
            long userId = Long.parseLong(first.get("id").toString());
            userDataDTO.setId(userId);
            userDataDTO.setUserId(userId);
            String projectStr = "";
            if (FmProperties.TenantConfig.Type.FM2_0.toString().equals(tenantConfig.getType())) {
                userDataDTO.setUserName(first.get("login_code").toString());
                userDataDTO.setPhone(first.get("telephone") == null ? "" : first.get("telephone").toString());
                projectStr = first.get("project") == null ? "" : first.get("project").toString();
            } else if (FmProperties.TenantConfig.Type.SHANG.toString().equals(tenantConfig.getType())) {
                userDataDTO.setUserName(first.get("user_name").toString());
                userDataDTO.setPhone(first.get("phone") == null ? "" : first.get("phone").toString());
                projectStr = first.get("proj_ids") == null ? "" : first.get("proj_ids").toString();
            }
            userDataDTO.setRealName(first.get("real_name").toString());
            // 查询项目信息
            // 判断是否是admin
            String projSql = null;
            if ("admin".equals(userDataDTO.getUserName()) && Objects.equals(userDataDTO.getUserId(), 1L)) {
                projSql = "select proj_id,proj_name from sys_project where deleted = false";
            } else {
                String projectIdStr = null;
                // 检查表存在：sys_user_project
                if (dbTool.checkTableExists(tenantConfig.getDbUrl(), tenantConfig.getDbUserName(), tenantConfig.getDbPassword(), "sys_user_project")) {
                    String userProjectSql = StrUtil.format("select project_id from sys_user_project where user_id = {}", first.get("id"));
                    DbTool.Request userProjectRequest = new DbTool.Request(tenantConfig.getDbUrl(), tenantConfig.getDbUserName(), tenantConfig.getDbPassword(), userProjectSql, 50);
                    DbTool.Response userProjectResponse = dbTool.apply(userProjectRequest);
                    if (userProjectResponse.success()) {
                        List<Map<String, Object>> userProjectData = userProjectResponse.data();
                        projectIdStr = userProjectData.stream().map(project -> project.get("project_id").toString()).collect(Collectors.joining(","));
                    } else {
                        projectIdStr = projectStr;
                    }
                }
                if (StrUtil.isBlank(projectIdStr)) {
                    String[] split = projectStr.split(",");
                    projectIdStr = new ArrayList<>(Arrays.asList(split)).stream().filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                }
                if (StrUtil.isNotBlank(projectIdStr)) {
                    projSql = StrUtil.format("select proj_id,proj_name from sys_project where proj_id IN ({}) AND deleted = false", projectIdStr);
                }
            }
            if (projSql != null) {
                DbTool.Request projectRequest = new DbTool.Request(tenantConfig.getDbUrl(), tenantConfig.getDbUserName(), tenantConfig.getDbPassword(), projSql, 50);
                DbTool.Response projectResponse = dbTool.apply(projectRequest);
                if (projectResponse.success()) {
                    List<Map<String, Object>> projectData = projectResponse.data();
                    userDataDTO.setProjects(projectData.stream().map(project -> new FmProjectDTO(Long.parseLong(project.get("proj_id").toString()), project.get("proj_name").toString())).toList());
                }
            } else {
                userDataDTO.setProjects(Lists.newArrayList());
            }
            return userDataDTO;
        }
        return null;
    }

    /**
     * 获取微信公众号用户信息
     *
     * @param openId      微信公众号openId
     * @param customerKey 微信公众号标识key
     * @return 微信公众号用户信息
     */
    @LogParam(printReturn = true)
    public FmWechatUserInfoDTO getTenantWechatUserInfo(String openId, String customerKey, FmProperties.TenantConfig tenantConfig) {
        String url = tenantConfig.getBaseUrl() + StrUtil.format("/ai/base/getUserByOpenId/{}/{}", openId, customerKey);
        String s = tenantFmHttpUtil(url, null, Method.GET, tenantConfig.getBearerToken());
        if (StrUtil.isNotBlank(s)) {
            JSONObject jsonObject = JSONUtil.parseObj(s);
            if (jsonObject.getInt("code") == 200) {
                return JSONUtil.toBean(jsonObject.getJSONObject("data"), FmWechatUserInfoDTO.class);
            }
        }
        return null;
    }

    /**
     * 获取指定/所有项目信息
     *
     * @param projectId 项目ID
     * @param tenantConfig 配置
     * @return 项目列表
     */
    @LogParam(printReturn = true)
    public List<FmProjectDTO> getTenantProjects(Long projectId, FmProperties.TenantConfig tenantConfig) {
        if (FmProperties.TenantConfig.ToolType.API.toString().equals(tenantConfig.getToolType())) {
            return getTenantProjectsFromApi(projectId, tenantConfig);
        } else if (FmProperties.TenantConfig.ToolType.DB.toString().equals(tenantConfig.getToolType())) {
            return getTenantProjectsFromDb(projectId, tenantConfig);
        } else {
            return Lists.newArrayList();
        }
    }

    private List<FmProjectDTO> getTenantProjectsFromApi(Long projectId, FmProperties.TenantConfig tenantConfig) {
        String url = tenantConfig.getBaseUrl() + "/ai/base/getProjects";
        url += "?projectId=" + (projectId == null ? 0 : projectId);
        String s = tenantFmHttpUtil(url, null, Method.GET, tenantConfig.getBearerToken());
        if (StrUtil.isNotBlank(s)) {
            JSONObject jsonObject = JSONUtil.parseObj(s);
            if (jsonObject.getInt("code") == 200) {
                JSONArray data = jsonObject.getJSONArray("data");
                return JSONUtil.toList(data, FmProjectDTO.class);
            }
        }
        return Lists.newArrayList();
    }

    private List<FmProjectDTO> getTenantProjectsFromDb(Long projectId, FmProperties.TenantConfig tenantConfig) {
        String projSql;
        if (projectId != null) {
            projSql = StrUtil.format("select proj_id,proj_name from sys_project where proj_id = {} AND deleted = 0", projectId);
        } else {
            projSql = "select proj_id,proj_name from sys_project where deleted = 0";
        }
        
        DbTool.Request projectRequest = new DbTool.Request(tenantConfig.getDbUrl(), tenantConfig.getDbUserName(), tenantConfig.getDbPassword(), projSql, 50);
        DbTool.Response projectResponse = dbTool.apply(projectRequest);
        if (projectResponse.success()) {
            List<Map<String, Object>> projectData = projectResponse.data();
            return projectData.stream().map(project -> new FmProjectDTO(Long.parseLong(project.get("proj_id").toString()), project.get("proj_name").toString())).toList();
        }
        return Lists.newArrayList();
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    public List<Map<String, Object>> getBuilding(Long projectId) {
        String result = getLocation(projectId, null, null);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (Objects.equals(jsonObject.getStr("code"), "200")) {
                JSONArray data = jsonObject.getJSONArray("data");
                return (List<Map<String, Object>>) (List<?>) data.toList(Map.class);
            }
        }
        return Collections.emptyList();
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    @Tool(name = "getFloor", description = "获取楼宇下的楼层列表")
    public List<Map<String, Object>> getFloor(Long projectId, Long buildingId) {
        String result = getLocation(projectId, buildingId, null);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (Objects.equals(jsonObject.getStr("code"), "200")) {
                JSONArray data = jsonObject.getJSONArray("data");
                return (List<Map<String, Object>>) (List<?>) data.toList(Map.class);
            }
        }
        return Collections.emptyList();
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    @Tool(name = "getRoom", description = "获取楼层下的房间列表")
    public List<Map<String, Object>> getRoom(Long projectId, Long buildingId, Long floorId) {
        String result = getLocation(projectId, buildingId, floorId);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (Objects.equals(jsonObject.getStr("code"), "200")) {
                JSONArray data = jsonObject.getJSONArray("data");
                return (List<Map<String, Object>>) (List<?>) data.toList(Map.class);
            }
        }
        return Collections.emptyList();
    }

    private String getLocation(Long projectId, Long buildingId, Long floorId) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("projectId", projectId);
        params.put("buildingId", buildingId);
        params.put("floorId", floorId);
        return fmHttpUtil("/ai/base/getLocation", JSONUtil.toJsonStr(params), Method.POST);
    }

    /**
     * 获取微信公众号的accessToken
     *
     * @param customerKey 微信公众号标识key
     * @return accessToken
     */
    @LogParam(printReturn = true)
    public String getWechatAccessToken(String customerKey) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("customerKey", customerKey);
        String s = fmHttpUtil("/ai/base/getWechatAccessToken", JSONUtil.toJsonStr(params), Method.POST);
        if (StrUtil.isNotBlank(s)) {
            JSONObject jsonObject = JSONUtil.parseObj(s);
            if (jsonObject.getInt("code") == 200) {
                return jsonObject.getStr("data");
            }
        }
        return null;
    }

    /**
     * FM接口请求工具类
     *
     * @param fullUrl     完整请求地址
     * @param jsonParam   请求参数
     * @param method      请求方式
     * @param bearerToken 请求鉴权参数
     * @return 响应结果
     */
    public static String tenantFmHttpUtil(String fullUrl, String jsonParam, Method method, String bearerToken) {
        HttpRequest httpRequest = HttpUtil.createRequest(method, fullUrl)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + bearerToken);
        if (StrUtil.isNotBlank(jsonParam)) {
            httpRequest.body(jsonParam);
        }
        log.info("FM接口请求参数：url:{}，method:{}，json参数：{}", fullUrl, method, jsonParam);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String body = httpResponse.body();
            log.info("FM接口请求结果：{}", body);
            return body;
        }
    }

    /**
     * FM接口请求工具类
     *
     * @param suffixUrl 接口后缀
     * @param jsonParam 请求参数
     * @param method    请求方式
     * @return 响应结果
     */
    public static String fmHttpUtil(String suffixUrl, String jsonParam, Method method) {
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        FmContentInfo fmContentInfo = contentInfo.getFmContentInfo();
        FmProperties.TenantConfig tenantConfig = fmContentInfo.getTenantConfig();
        String url = tenantConfig.getBaseUrl() + suffixUrl;

        HttpRequest httpRequest = HttpUtil.createRequest(method, url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + tenantConfig.getBearerToken());
        if (StrUtil.isNotBlank(jsonParam)) {
            httpRequest.body(jsonParam);
        }
        log.info("FM接口请求参数：url:{}，method:{}，json参数：{}", url, method, jsonParam);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String body = httpResponse.body();
            log.info("FM接口请求结果：{}", body);
            return body;
        }
    }
}

