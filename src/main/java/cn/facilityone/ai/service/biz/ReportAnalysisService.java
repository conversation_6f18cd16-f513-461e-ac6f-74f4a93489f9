package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.entity.AiChatResponse;
import cn.facilityone.ai.service.tool.ChartTool;
import cn.facilityone.ai.util.PromptsUtil;
import cn.facilityone.ai.util.agentic.EvaluatorOptimizer;
import cn.facilityone.ai.util.agentic.OrchestratorWorkers;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报表分析服务
 * 包含工具：1、报表读取工具 2、报表处理工具 3、报表生成工具
 */
@Service
public class ReportAnalysisService {

    @Autowired
    private ChartTool chartTool;

    private final ChatClient defaultChatClient;

    public ReportAnalysisService(@Qualifier("chatClient") ChatClient defaultChatClient) {
        this.defaultChatClient = defaultChatClient;
    }

    public String analyzeByChatGPT(String workOrderData) {
        // 这里应该调用LLM服务，使用report-analysis.md中的提示词
        // 示例代码，实际实现需要根据项目中的LLM调用方式进行调整
        String prompt = PromptsUtil.getPrompts("fm/report-analysis3");
        prompt = prompt.replace("{{工单数据}}", workOrderData);

        // 调用LLM API
        String content = defaultChatClient
                .prompt(prompt)
                .tools(chartTool)
                .call().content();
//        OrchestratorWorkers orchestratorWorkers = new OrchestratorWorkers(defaultChatClient);
//        String content = orchestratorWorkers.process(prompt).analysis();

//        EvaluatorOptimizer evaluatorOptimizer = new EvaluatorOptimizer(defaultChatClient);
//        String content = evaluatorOptimizer.loop(prompt).solution();
        // 临时返回，实际应返回LLM的分析结果
        return content;
    }

    /**
     * 读取提示词文件内容
     *
     * @param path 提示词文件路径
     * @return 提示词内容
     */
    private String readPromptFile(String path) {
        try {
            return new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(path)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 将CSV字段数组解析为工单数据对象
     * 
     * @param values CSV字段数组
     * @return 工单数据对象
     */
    private WorkOrderData parseWorkOrderData(String[] values) {
        WorkOrderData workOrder = new WorkOrderData();
        
        if (values.length > 0) workOrder.setOrderNumber(values[0]);
        if (values.length > 1) workOrder.setApplicant(values[1]);
        if (values.length > 2) workOrder.setCreateDate(values[2]);
        if (values.length > 3) workOrder.setCreateTime(values[3]);
        if (values.length > 4) workOrder.setPublisher(values[4]);
        if (values.length > 5) workOrder.setPublishDate(values[5]);
        if (values.length > 6) workOrder.setPublishTime(values[6]);
        if (values.length > 7) workOrder.setExecutor(values[7]);
        if (values.length > 8) workOrder.setAcceptDate(values[8]);
        if (values.length > 9) workOrder.setAcceptTime(values[9]);
        
        // 服务类型有多个字段，合并处理
        StringBuilder serviceType = new StringBuilder();
        for (int i = 10; i <= 13; i++) {
            if (values.length > i && values[i] != null && !values[i].trim().isEmpty()) {
                if (!serviceType.isEmpty()) {
                    serviceType.append(" / ");
                }
                serviceType.append(values[i].trim());
            }
        }
        workOrder.setServiceType(serviceType.toString());
        
        if (values.length > 14) workOrder.setPriority(values[14]);
        
        // 部门有多个字段，合并处理
        StringBuilder department = new StringBuilder();
        for (int i = 15; i <= 17; i++) {
            if (values.length > i && values[i] != null && !values[i].trim().isEmpty()) {
                if (!department.isEmpty()) {
                    department.append(" / ");
                }
                department.append(values[i].trim());
            }
        }
        workOrder.setDepartment(department.toString());
        
        if (values.length > 18) workOrder.setBuilding(values[18]);
        if (values.length > 19) workOrder.setFloor(values[19]);
        if (values.length > 20) workOrder.setRoom(values[20]);
        if (values.length > 21) workOrder.setFullLocation(values[21]);
        if (values.length > 22) workOrder.setDescription(values[22]);
        if (values.length > 23) workOrder.setOrderStatus(values[23]);
        if (values.length > 24) workOrder.setPauseReason(values[24]);
        if (values.length > 25) workOrder.setChargeable(values[25]);
        if (values.length > 26) workOrder.setEstimatedStartDate(values[26]);
        if (values.length > 27) workOrder.setEstimatedStartTime(values[27]);
        if (values.length > 28) workOrder.setEstimatedEndDate(values[28]);
        if (values.length > 29) workOrder.setEstimatedEndTime(values[29]);
        if (values.length > 30) workOrder.setEstimatedWorkDuration(values[30]);
        if (values.length > 31) workOrder.setActualStartDate(values[31]);
        if (values.length > 32) workOrder.setActualStartTime(values[32]);
        if (values.length > 33) workOrder.setActualEndDate(values[33]);
        if (values.length > 34) workOrder.setActualEndTime(values[34]);
        if (values.length > 35) workOrder.setActualWorkDuration(values[35]);
        if (values.length > 36) workOrder.setOrderPauseDuration(values[36]);
        if (values.length > 37) workOrder.setWorkContent(values[37]);
        
        return workOrder;
    }
    
    /**
     * 工单数据类
     */
    @Setter
    @Getter
    public static class WorkOrderData {
        // Getter和Setter方法
        private String orderNumber;       // 工单号
        private String applicant;         // 申请人
        private String createDate;        // 创建日期
        private String createTime;        // 创建时间
        private String publisher;         // 发布人
        private String publishDate;       // 发布日期
        private String publishTime;       // 发布时间
        private String executor;          // 执行人
        private String acceptDate;        // 接单日期
        private String acceptTime;        // 接单时间
        private String serviceType;       // 服务类型
        private String priority;          // 优先级
        private String department;        // 部门
        private String building;          // 大厦
        private String floor;             // 楼层
        private String room;              // 房间
        private String fullLocation;      // 完整位置
        private String description;       // 描述
        private String orderStatus;       // 工单状态
        private String pauseReason;       // 暂停原因
        private String chargeable;        // 是否收费
        private String estimatedStartDate;// 预估开始日期
        private String estimatedStartTime;// 预估开始时间
        private String estimatedEndDate;  // 预估完成日期
        private String estimatedEndTime;  // 预估完成时间
        private String estimatedWorkDuration; // 预计工作时长
        private String actualStartDate;   // 实际开始日期
        private String actualStartTime;   // 实际开始时间
        private String actualEndDate;     // 实际完成日期
        private String actualEndTime;     // 实际完成时间
        private String actualWorkDuration;// 实际工作时长(分钟)
        private String orderPauseDuration;// 工单暂停时长(分钟)
        private String workContent;       // 工作内容

        /**
         * 获取创建日期时间的Date对象
         * 
         * @return 创建日期时间
         */
        public Date getCreateDateTime() {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
                return sdf.parse(createDate + " " + createTime);
            } catch (ParseException e) {
                return null;
            }
        }
        
        /**
         * 获取发布日期时间的Date对象
         * 
         * @return 发布日期时间
         */
        public Date getPublishDateTime() {
            try {
                if (publishDate == null || publishDate.isEmpty() || 
                    publishTime == null || publishTime.isEmpty()) {
                    return null;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
                return sdf.parse(publishDate + " " + publishTime);
            } catch (ParseException e) {
                return null;
            }
        }
        
        /**
         * 获取接单日期时间的Date对象
         * 
         * @return 接单日期时间
         */
        public Date getAcceptDateTime() {
            try {
                if (acceptDate == null || acceptDate.isEmpty() || 
                    acceptTime == null || acceptTime.isEmpty()) {
                    return null;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
                return sdf.parse(acceptDate + " " + acceptTime);
            } catch (ParseException e) {
                return null;
            }
        }
        
        /**
         * 获取实际开始日期时间的Date对象
         * 
         * @return 实际开始日期时间
         */
        public Date getActualStartDateTime() {
            try {
                if (actualStartDate == null || actualStartDate.isEmpty() || 
                    actualStartTime == null || actualStartTime.isEmpty()) {
                    return null;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
                return sdf.parse(actualStartDate + " " + actualStartTime);
            } catch (ParseException e) {
                return null;
            }
        }
        
        /**
         * 获取实际完成日期时间的Date对象
         * 
         * @return 实际完成日期时间
         */
        public Date getActualEndDateTime() {
            try {
                if (actualEndDate == null || actualEndDate.isEmpty() || 
                    actualEndTime == null || actualEndTime.isEmpty()) {
                    return null;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
                return sdf.parse(actualEndDate + " " + actualEndTime);
            } catch (ParseException e) {
                return null;
            }
        }
    }

    /**
     * 多维度分析工单数据
     * 
     * @param workOrders 工单数据列表
     * @return 多维度分析结果
     */
    public WorkOrderAnalysisResult analyzeWorkOrders(List<WorkOrderData> workOrders) {
        if (workOrders == null || workOrders.isEmpty()) {
            return WorkOrderAnalysisResult.builder()
                    .totalCount(0)
                    .build();
        }

        // 1. 基础统计
        int totalCount = workOrders.size();
        
        // 2. 按状态分类统计
        Map<String, Integer> statusDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String status = workOrder.getOrderStatus();
            if (status == null || status.isEmpty()) {
                status = "未知";
            }
            statusDistribution.put(status, statusDistribution.getOrDefault(status, 0) + 1);
        }
        
        // 3. 按优先级分类统计
        Map<String, Integer> priorityDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String priority = workOrder.getPriority();
            if (priority == null || priority.isEmpty()) {
                priority = "未知";
            }
            priorityDistribution.put(priority, priorityDistribution.getOrDefault(priority, 0) + 1);
        }
        
        // 4. 按服务类型分类统计
        Map<String, Integer> serviceTypeDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String serviceType = workOrder.getServiceType();
            if (serviceType == null || serviceType.isEmpty()) {
                serviceType = "未知";
            }
            serviceTypeDistribution.put(serviceType, serviceTypeDistribution.getOrDefault(serviceType, 0) + 1);
        }
        
        // 5. 按部门分类统计
        Map<String, Integer> departmentDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String department = workOrder.getDepartment();
            if (department == null || department.isEmpty()) {
                department = "未知";
            }
            departmentDistribution.put(department, departmentDistribution.getOrDefault(department, 0) + 1);
        }
        
        // 6. 按位置分类统计
        Map<String, Integer> locationDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String building = workOrder.getBuilding();
            if (building == null || building.isEmpty()) {
                building = "未知";
            }
            locationDistribution.put(building, locationDistribution.getOrDefault(building, 0) + 1);
        }
        
        // 7. 按时间维度统计 - 每月工单数量
        Map<String, Integer> monthlyDistribution = new HashMap<>();
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
        for (WorkOrderData workOrder : workOrders) {
            Date createDate = workOrder.getCreateDateTime();
            if (createDate != null) {
                String month = monthFormat.format(createDate);
                monthlyDistribution.put(month, monthlyDistribution.getOrDefault(month, 0) + 1);
            }
        }
        
        // 8. 按时间维度统计 - 每周几工单数量
        Map<String, Integer> weekdayDistribution = new HashMap<>();
        String[] weekdays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        for (WorkOrderData workOrder : workOrders) {
            Date createDate = workOrder.getCreateDateTime();
            if (createDate != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(createDate);
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1; // 0-6
                String weekday = weekdays[dayOfWeek];
                weekdayDistribution.put(weekday, weekdayDistribution.getOrDefault(weekday, 0) + 1);
            }
        }
        
        // 9. 按时间维度统计 - 每天时段工单数量
        Map<String, Integer> hourlyDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            Date createDate = workOrder.getCreateDateTime();
            if (createDate != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(createDate);
                int hour = calendar.get(Calendar.HOUR_OF_DAY);
                String hourRange = hour + ":00-" + (hour + 1) + ":00";
                hourlyDistribution.put(hourRange, hourlyDistribution.getOrDefault(hourRange, 0) + 1);
            }
        }
        
        // 10. 计算平均处理时间（实际完成时间-实际开始时间）
        List<Long> processingTimes = new ArrayList<>();
        for (WorkOrderData workOrder : workOrders) {
            Date actualEndDate = workOrder.getActualEndDateTime();
            Date actualStartDate = workOrder.getActualStartDateTime();
            
            if (actualEndDate != null && actualStartDate != null) {
                long processingTimeMs = actualEndDate.getTime() - actualStartDate.getTime();
                // 只添加有效的处理时间（大于0）
                if (processingTimeMs > 0) {
                    processingTimes.add(processingTimeMs);
                }
            }
        }
        
        double avgProcessingTimeMinutes = 0;
        if (!processingTimes.isEmpty()) {
            double totalMinutes = 0;
            for (Long time : processingTimes) {
                totalMinutes += time / (1000.0 * 60); // 转换为分钟
            }
            avgProcessingTimeMinutes = totalMinutes / processingTimes.size();
        }
        
        // 11. 计算工单完成率
        long completedCount = workOrders.stream()
                .filter(wo -> "已完成".equals(wo.getOrderStatus()) || "已关闭".equals(wo.getOrderStatus()) || "已存档".equals(wo.getOrderStatus()))
                .count();
        double completionRate = totalCount > 0 ? (double) completedCount / totalCount : 0;
        
        // 12. 按执行人统计工单数量
        Map<String, Integer> executorDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String executor = workOrder.getExecutor();
            if (executor == null || executor.isEmpty()) {
                executor = "未分配";
            }
            executorDistribution.put(executor, executorDistribution.getOrDefault(executor, 0) + 1);
        }
        
        // 13. 按是否收费统计
        Map<String, Integer> chargeableDistribution = new HashMap<>();
        for (WorkOrderData workOrder : workOrders) {
            String chargeable = workOrder.getChargeable();
            if (chargeable == null || chargeable.isEmpty()) {
                chargeable = "未知";
            }
            chargeableDistribution.put(chargeable, chargeableDistribution.getOrDefault(chargeable, 0) + 1);
        }
        
        // 构建并返回分析结果
        return WorkOrderAnalysisResult.builder()
                .totalCount(totalCount)
                .statusDistribution(sortMapByValue(statusDistribution))
                .priorityDistribution(sortMapByValue(priorityDistribution))
                .serviceTypeDistribution(sortMapByValue(serviceTypeDistribution))
                .departmentDistribution(sortMapByValue(departmentDistribution))
                .locationDistribution(sortMapByValue(locationDistribution))
                .monthlyDistribution(new LinkedHashMap<>(monthlyDistribution))
                .weekdayDistribution(sortMapByWeekday(weekdayDistribution))
                .hourlyDistribution(sortMapByHour(hourlyDistribution))
                .avgProcessingTimeMinutes(avgProcessingTimeMinutes)
                .completionRate(completionRate)
                .executorDistribution(sortMapByValue(executorDistribution))
                .chargeableDistribution(sortMapByValue(chargeableDistribution))
                .build();
    }
    
    /**
     * 按值对Map进行排序（降序）
     * 
     * @param map 需要排序的Map
     * @return 排序后的Map
     */
    private <K> Map<K, Integer> sortMapByValue(Map<K, Integer> map) {
        return map.entrySet().stream()
                .sorted(Map.Entry.<K, Integer>comparingByValue().reversed())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }
    
    /**
     * 按周几对Map进行排序
     * 
     * @param map 需要排序的Map
     * @return 排序后的Map
     */
    private Map<String, Integer> sortMapByWeekday(Map<String, Integer> map) {
        String[] weekdays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        Map<String, Integer> sortedMap = new LinkedHashMap<>();
        
        for (String weekday : weekdays) {
            if (map.containsKey(weekday)) {
                sortedMap.put(weekday, map.get(weekday));
            }
        }
        
        return sortedMap;
    }
    
    /**
     * 按小时对Map进行排序
     * 
     * @param map 需要排序的Map
     * @return 排序后的Map
     */
    private Map<String, Integer> sortMapByHour(Map<String, Integer> map) {
        Map<String, Integer> sortedMap = new LinkedHashMap<>();
        
        for (int i = 0; i < 24; i++) {
            String hourRange = i + ":00-" + (i + 1) + ":00";
            if (map.containsKey(hourRange)) {
                sortedMap.put(hourRange, map.get(hourRange));
            }
        }
        
        return sortedMap;
    }
    
    /**
     * 工单分析结果类
     */
    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkOrderAnalysisResult {
        private int totalCount;                              // 工单总数
        private Map<String, Integer> statusDistribution;     // 状态分布
        private Map<String, Integer> priorityDistribution;   // 优先级分布
        private Map<String, Integer> serviceTypeDistribution;// 服务类型分布
        private Map<String, Integer> departmentDistribution; // 部门分布
        private Map<String, Integer> locationDistribution;   // 位置分布
        private Map<String, Integer> monthlyDistribution;    // 月度分布
        private Map<String, Integer> weekdayDistribution;    // 周几分布
        private Map<String, Integer> hourlyDistribution;     // 小时分布
        private double avgProcessingTimeMinutes;             // 平均处理时间(分钟)
        private double completionRate;                       // 完成率
        private Map<String, Integer> executorDistribution;   // 执行人分布
        private Map<String, Integer> chargeableDistribution; // 收费情况分布
    }
}