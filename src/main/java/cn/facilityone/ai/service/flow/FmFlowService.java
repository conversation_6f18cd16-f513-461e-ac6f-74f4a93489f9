package cn.facilityone.ai.service.flow;

import cn.facilityone.ai.config.chat.ChatMessageMeta;
import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.constant.CommonConstant;
import cn.facilityone.ai.constant.FmFlowConstant;
import cn.facilityone.ai.dto.*;
import cn.facilityone.ai.entity.*;
import cn.facilityone.ai.properties.FmProperties;
import cn.facilityone.ai.service.tool.FmBaseTool;
import cn.facilityone.ai.service.tool.FmReqTool;
import cn.facilityone.ai.service.tool.FmReqWechatTool;
import cn.facilityone.ai.service.tool.FmWoTool;
import cn.facilityone.ai.util.PromptsUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13 14:49
 */

@Slf4j
@Service
public class FmFlowService {

    private final FmBaseTool fmBaseTool;
    private final FmReqTool fmReqTool;
    private final FmReqWechatTool fmReqWechatTool;
    private final FmWoTool fmWoTool;
    private final ChatClient defaultChatClient;
    private final ChatClient intentClient;
    private final ChatMemory chatMemory;
    private final VectorStore vectorStore;
    private final FmProperties fmProperties;

    @Autowired
    public FmFlowService(@Qualifier("chatClient") ChatClient chatClient, @Qualifier("intentClient") ChatClient intentClient,
                         FmBaseTool fmBaseTool, FmReqTool fmReqTool, FmReqWechatTool fmReqWechatTool, FmWoTool fmWoTool,
                         ChatMemory chatMemory, VectorStore vectorStore, FmProperties fmProperties) {
        this.defaultChatClient = chatClient;
        this.intentClient = intentClient;
        this.fmBaseTool = fmBaseTool;
        this.fmReqTool = fmReqTool;
        this.fmReqWechatTool = fmReqWechatTool;
        this.fmWoTool = fmWoTool;
        this.chatMemory = chatMemory;
        this.vectorStore = vectorStore;
        this.fmProperties = fmProperties;
    }


    public Flux<AiChatResponse> handleFmFlow(String message, Long projectId, boolean isStream) {
        // 获取对话用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        String conversationId = contentInfo.getConversationId();

        // 初始化项目信息
        if (contentInfo.getFmContentInfo() == null) {
            return Flux.just(AiChatResponse.createResponse("用户不存在或信息获取失败", conversationId));
        }
        initFmContentProject(contentInfo, projectId);
        if (contentInfo.getFmContentInfo().getFmProjectId() == null && (contentInfo.getFmContentInfo().getProjectList() == null || contentInfo.getFmContentInfo().getProjectList().isEmpty())) {
            return Flux.just(AiChatResponse.createResponse("项目不存在", conversationId));
        }
        String system = "当前上下文信息如下：" + contentInfo;

        return switch (contentInfo.getAppCode()) {
            case "fm-repair-wechat-h5" -> processWeChatH5ReqFlow(message, system, conversationId, isStream);
            case "fm-mix-fm-app" -> processFmAppMixFlow(message, system, conversationId, isStream);
            case "fm-mix-coe-web-demo" -> processCoeWebDemoOutSideMixFlow(message, system, conversationId, isStream);
            case null, default ->
                    Flux.just(AiChatResponse.createResponse("该工作流暂不存在，请稍后再试", conversationId));
        };
    }

    /**
     * 处理需求单流程
     */
    private Flux<AiChatResponse> processWeChatH5ReqFlow(String message, String system, String conversationId, boolean isStream) {
        // 固定返回1
        if ("快速报修".equals(message)) {
            // 添加用户输入到消息记录中
            Message inputMessage = new UserMessage(message);
            chatMemory.add(conversationId, inputMessage);
            String msg = FmFlowConstant.WELCOME_MESSAGE_REQUIREMENT_TEMPLATE;
            // 添加返回值到消息记录中
            Message respMessage = new AssistantMessage(msg);
            chatMemory.add(conversationId, respMessage);
            return Flux.just(AiChatResponse.createResponse(msg, conversationId));
        }

        // 获取历史消息记录
        List<Message> messages = chatMemory.get(conversationId);
        // 判断是查询需求单还是提交需求单
        String intent = intentClient.prompt()
                .user(u -> u.text(PromptsUtil.getPrompts("fm/req-intentionJudgment"))
                        .params(Map.of("inputText", message, "historyMessages", messages)))
                .call().content();

        if ("S".equals(intent) || "需求查询".equals(message) || "需求查询。".equals(message)) {
            // 搜索逻辑
            ChatClient.ChatClientRequestSpec clientRequestSpec = defaultChatClient
                    .prompt(PromptsUtil.getPrompts("fm/req-query"))
                    .system(system)
                    .user(message)
                    .advisors(MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build())
                    .tools(fmReqWechatTool);

            if (isStream) {
                return clientRequestSpec.stream().content().map(r -> {
                    String msg = new String(r.getBytes(), StandardCharsets.UTF_8);
                    return AiChatResponse.createResponse(msg, conversationId);
                });
            } else {
                String msg = clientRequestSpec.call().content();
                return Flux.just(AiChatResponse.createResponse(msg, conversationId));
            }
        } else if ("A".equals(intent)) {
            // 提报需求单逻辑
            // 预查询部分数据：需求分类信息、楼宇信息
            ApiAuthenticationToken authentication =
                    (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
            ContentInfo contentInfo = authentication.getContentInfo();
            FmContentInfo fmContentInfo = contentInfo.getFmContentInfo();
            List<Map<String, Object>> reqTypeList = fmReqTool.getReqType(fmContentInfo.getFmProjectId());
            List<Map<String, Object>> buildingList = fmBaseTool.getBuilding(fmContentInfo.getFmProjectId());

            ChatClient.ChatClientRequestSpec clientRequestSpec = defaultChatClient
                    .prompt(PromptsUtil.getPrompts("fm/req-create"))
                    .system(system + "\navailableReqTypes：" + JSONUtil.toJsonStr(reqTypeList) + "\navailableBuildingsList：" + JSONUtil.toJsonStr(buildingList))
                    .user(message)
                    .advisors(MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build())
                    .tools(fmBaseTool, fmReqWechatTool);
            if (isStream) {
                return clientRequestSpec.stream().content().map(r -> {
                    String msg = new String(r.getBytes(), StandardCharsets.UTF_8);
                    return AiChatResponse.createResponse(msg, conversationId);
                });
            } else {
                String msg = clientRequestSpec.call().content();
                return Flux.just(AiChatResponse.createResponse(msg, conversationId));
            }
        } else {
            // 其它逻辑
            return Flux.just(AiChatResponse.createResponse("我专注于处理FM系统需求单的查询与提报，目前暂不提供其它服务，敬请期待后续更新。", conversationId));
        }
    }

    /**
     * 处理FM-APP混合工作流
     *
     * @param message        消息内容
     * @param system         系统上下文
     * @param conversationId 会话ID
     * @param isStream       是否流式响应
     * @return 响应结果
     */
    private Flux<AiChatResponse> processFmAppMixFlow(String message, String system, String conversationId, boolean isStream) {
        // 固定处理
        List<Message> historyMessage = chatMemory.get(conversationId);
        String userLastMessage = null;
        if (historyMessage.size() - 2 >= 0) {
            userLastMessage = historyMessage.get(historyMessage.size() - 2).getText();
        }
        HashSet<String> fixedContentSet = Sets.newHashSet("操作问题", "一键报单", "快速报修", "进度查询", "工单", "需求", "跳转测试");
        String message1 = message.endsWith("。") ? message.substring(0, message.length() - 1) : message;
        if (fixedContentSet.contains(message1)) {
            // 保存聊天内容
            chatMemory.add(conversationId, new UserMessage(message));
            // 处理返回值
            String msg = "";
            List<SuggestedQuestion> suggestedQuestionList = new ArrayList<>();
            if ("操作问题".equals(message1)) {
                List<String> questionList = FmFlowConstant.OPERATION_QUESTION_SUGGESTION_LIST;
                // 随机排序
                Collections.shuffle(questionList);
                String questionStr = StrUtil.format(FmFlowConstant.OPERATION_QUESTION_SUGGESTION_MSG_MODEL,
                        questionList.get(0), questionList.get(0),
                        questionList.get(1), questionList.get(1),
                        questionList.get(2), questionList.get(2),
                        questionList.get(3), questionList.get(3));
                // 添加建议问题
                SuggestedQuestion suggestedQuestion = new SuggestedQuestion(questionStr, SuggestedQuestion.QuestionFormat.APP_TEMPLATE);
                suggestedQuestionList.add(suggestedQuestion);
                msg = "您可以试着问我：";
            } else if ("一键报单".equals(message1)) {
                msg = "好的，请您描述一下具体问题，我来为您填写工单。";
            } else if ("快速报修".equals(message1)) {
                msg = "好的，请您描述一下具体需求，我来为您填报需求。";
            } else if ("进度查询".equals(message1)) {
                msg = "好的，请问您要查询工单进度还是需求进度？";
                // 添加建议问题
                SuggestedQuestion suggestedQuestion = new SuggestedQuestion(FmFlowConstant.WO_OR_REQUIRE_CHOOSE_SUGGESTION, SuggestedQuestion.QuestionFormat.APP_TEMPLATE);
                suggestedQuestionList.add(suggestedQuestion);
            } else if ("工单".equals(message1) && "进度查询".equals(userLastMessage)) {
                msg = "好的，请您提供一下需要跟进的工单编号、具体内容描述或提交时间，以便为您精准查询处理进度～";
            } else if ("需求".equals(message1) && "进度查询".equals(userLastMessage)) {
                msg = "好的，请您提供一下需要跟进的需求编号、具体内容描述或提交时间，以便为您精准查询处理进度～";
            } else if ("跳转测试".equals(message1)) {
                msg = "点击前往[创建需求单](jump://fone-fm/m-requirement?projectId=1&name=张三&phone=13812345678)";
            }

            // 保存出记录，返回数据
            chatMemory.add(conversationId, new AssistantMessage(msg));
            return Flux.just(AiChatResponse.createResponse(msg, conversationId, new AiChatResponseMeta(suggestedQuestionList)));
        }

        // 特殊测试
        if ("CUT测试".equals(message)) {
            Flux<AiChatResponse> flux1 = Flux.just(AiChatResponse.createResponse("CUT测试", conversationId));
            Flux<AiChatResponse> flux2 = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.CUT)));
            Flux<AiChatResponse> flux3 = Flux.just(AiChatResponse.createResponse("第二条消息", conversationId));
            Flux<AiChatResponse> flux4 = Flux.just(AiChatResponse.createResponse("前往需求页面", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, "jump://fone-fm/m-requirement")));
            return Flux.concat(flux1, flux2, flux3, flux4);
        }

        // 分类：1、操作问题查询：H；2、需求提报：R；3、工单提报:F；4、需求查询:C；5、工单查询:S；6、其它:O
        List<Message> messages = chatMemory.get(conversationId);
        String intent = intentClient.prompt()
                .user(u -> u.text(PromptsUtil.getPrompts("fm/mix-app-intentionJudgment"))
                        .params(Map.of("inputText", message, "historyMessages", messages)))
                .call().content();

        // 获取对话用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        FmContentInfo fmContentInfo = contentInfo.getFmContentInfo();
        // 操作问题查询
        switch (intent) {
            case "H" -> {
                // 查询知识库并回答提问
                var qaAdvisor = QuestionAnswerAdvisor.builder(vectorStore)
                        .searchRequest(SearchRequest.builder()
                                .similarityThreshold(0.6d)
                                .topK(5)
                                .filterExpression(new FilterExpressionBuilder()
                                        .eq("appId", contentInfo.getAppId())
                                        .build()
                                ).build())
                        .build();
                ChatClient.ChatClientRequestSpec clientRequestSpec = defaultChatClient
                        .prompt(PromptsUtil.getPrompts("fm/mix-app-knowledgeBaseQuery"))
                        .user(message)
                        .advisors(
                                MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build(),
                                qaAdvisor
                        );
                Flux<AiChatResponse> llmFlux;
                if (isStream) {
                    llmFlux = clientRequestSpec.stream().content().map(r -> {
                        String msg = new String(r.getBytes(), StandardCharsets.UTF_8);
                        return AiChatResponse.createResponse(msg, conversationId);
                    });
                } else {
                    String msg = clientRequestSpec.call().content();
                    llmFlux = Flux.just(AiChatResponse.createResponse(msg, conversationId));
                }
                // 判断归属模块，增加跳转链接
                String module = intentClient.prompt()
                        .user(u -> u.text(PromptsUtil.getPrompts("fm/mix-app-questionModule"))
                                .param("inputText", message).param("historyMessages", messages))
                        .call().content();
                String moduleName = "";
                String moduleJumpUrl = "";
                if (module != null) {
                    // 获取模块名称
                    FmAppIntentRouterDTO fmAppIntentRouterDTO = FmAppIntentRouterDTO.indexByIntent(module, FmFlowConstant.APP_INTENT_ROUTER_SET);
                    if (fmAppIntentRouterDTO != null) {
                        moduleName = fmAppIntentRouterDTO.getName();
                        moduleJumpUrl = fmAppIntentRouterDTO.getRouter();
                    }
                }
                Flux<AiChatResponse> jumpFlux = null;
                Flux<AiChatResponse> cutFlux = null;
                if (StrUtil.isNotBlank(moduleName)) {
                    cutFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.CUT)));
                    String msg = StrUtil.format("已为您找到{}功能，点击下方入口进入", moduleName);
                    String jumpUrl = StrUtil.format("jump://fone-fm/{}", moduleJumpUrl);
                    jumpFlux = Flux.just(AiChatResponse.createResponse(msg, conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, jumpUrl)));
                }
                return jumpFlux == null ? llmFlux : Flux.concat(llmFlux, cutFlux, jumpFlux);
            }
            case "R" -> {
                // 需求提报
                // 先返回提示
                Flux<AiChatResponse> prefixFlux = Flux.just(AiChatResponse.createResponse("好的，您的需求已收到，正在为您填写需求……", conversationId), AiChatResponse.createOperationResponse(conversationId, AiChatResponseMeta.ExtraAction.CUT));
                // 预获取部分参数
                List<Map<String, Object>> reqTypeList = fmReqTool.getReqType(fmContentInfo.getFmProjectId());
                List<Map<String, Object>> buildingList = fmBaseTool.getBuilding(fmContentInfo.getFmProjectId());
                // 调用模型
                String jumpUrl = defaultChatClient
                        .prompt(PromptsUtil.getPrompts("fm/mix-app-reqCreate"))
                        .system(system + "\navailableReqTypes：" + JSONUtil.toJsonStr(reqTypeList) + "\navailableBuildingsList：" + JSONUtil.toJsonStr(buildingList))
                        .user(message)
                        .advisors(MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build())
                        .tools(fmBaseTool).call().content();
                Flux<AiChatResponse> jumpFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, jumpUrl)));
                return Flux.concat(prefixFlux, jumpFlux);
            }
            case "F" -> {
                // 工单提报
                Flux<AiChatResponse> prefixFlux = Flux.just(AiChatResponse.createResponse("好的，您的问题已收到，正在为您填写工单……", conversationId), AiChatResponse.createOperationResponse(conversationId, AiChatResponseMeta.ExtraAction.CUT));
                // 预获取部分参数
                List<Map<String, Object>> serviceTypeList = fmWoTool.getServiceType(fmContentInfo.getFmProjectId());
                List<Map<String, Object>> buildingList = fmBaseTool.getBuilding(fmContentInfo.getFmProjectId());
                // 调用模型生成跳转链接
                String jumpUrl = defaultChatClient
                        .prompt(PromptsUtil.getPrompts("fm/mix-app-woCreate"))
                        .system(system + "\navailableServiceTypeList：" + JSONUtil.toJsonStr(serviceTypeList) + "\navailableBuildingsList：" + JSONUtil.toJsonStr(buildingList))
                        .user(message)
                        .advisors(MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build())
                        .tools(fmBaseTool, fmWoTool).call().content();
                Flux<AiChatResponse> jumpFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, jumpUrl)));
                return Flux.concat(prefixFlux, jumpFlux);
            }
            case "C" -> {
                // 需求查询
                chatMemory.add(conversationId, new UserMessage(message)); // 添加聊天记录
                String preTips = "好的，正在为您查找需求";
                Flux<AiChatResponse> prefixFlux = Flux.just(AiChatResponse.createResponse(preTips, conversationId));
                // 调用模型生成跳转链接
                String jumpUrl = defaultChatClient
                        .prompt(PromptsUtil.getPrompts("fm/mix-app-reqQuery"))
                        .system(system)
                        .user(message)
                        .call().content();
                Flux<AiChatResponse> llmFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, jumpUrl)));
                ChatMessageMeta chatMessageMeta = new ChatMessageMeta(AiChatResponseMeta.ExtraAction.JUMP, "", jumpUrl);
                chatMemory.add(conversationId, new AssistantMessage(StrUtil.format("{}...", preTips), chatMessageMeta.toMap())); // 添加聊天记录
                return Flux.concat(prefixFlux, llmFlux);
            }
            case "S" -> {
                // 工单查询
                chatMemory.add(conversationId, new UserMessage(message)); // 添加聊天记录
                String preTips = "好的，正在为您查找工单";
                Flux<AiChatResponse> prefixFlux = Flux.just(AiChatResponse.createResponse(preTips, conversationId));
                // 调用模型生成跳转链接
                String jumpUrl = defaultChatClient
                        .prompt(PromptsUtil.getPrompts("fm/mix-app-woQuery"))
                        .system(system)
                        .user(message)
                        .call().content();
                Flux<AiChatResponse> llmFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.JUMP, jumpUrl)));
                ChatMessageMeta chatMessageMeta = new ChatMessageMeta(AiChatResponseMeta.ExtraAction.JUMP, "", jumpUrl);
                chatMemory.add(conversationId, new AssistantMessage(StrUtil.format("{}...", preTips), chatMessageMeta.toMap())); // 添加聊天记录
                return Flux.concat(prefixFlux, llmFlux);
            }
            case null, default -> {
                // 其它逻辑
                return Flux.just(AiChatResponse.createResponse("这个问题有点难，我还要再学习下再回答你。你可以问我设施管理系统相关的问题~", conversationId));
            }
        }
    }

    /**
     * COE-WEB-多FM演示环境-外层-客服&路径跳转&报表分析
     *
     * @param message        输入
     * @param system         系统上下文
     * @param conversationId 会话ID
     * @param isStream       是否流式返回
     * @return 响应
     */
    private Flux<AiChatResponse> processCoeWebDemoOutSideMixFlow(String message, String system, String conversationId, boolean isStream) {
        // 1.固定处理
        HashSet<String> fixedContentSet = Sets.newHashSet("操作问题");
        String message1 = message.endsWith("。") ? message.substring(0, message.length() - 1) : message;
        if (fixedContentSet.contains(message1)) {
            // 保存聊天内容
            chatMemory.add(conversationId, new UserMessage(message));
            // 处理返回值
            String msg = "";
            List<SuggestedQuestion> suggestedQuestionList = new ArrayList<>();
            if ("操作问题".equals(message1)) {
                List<String> questionList = FmFlowConstant.DEMO_WEB_OUT_OPERATION_QUESTION_SUGGESTION_LIST;
                // 随机排序
                Collections.shuffle(questionList);
                String questionStr = StrUtil.format(FmFlowConstant.DEMO_WEB_OUT_OPERATION_QUESTION_SUGGESTION_MSG_MODEL,
                        questionList.get(0), questionList.get(0),
                        questionList.get(1), questionList.get(1),
                        questionList.get(2), questionList.get(2),
                        questionList.get(3), questionList.get(3));
                // 添加建议问题
                SuggestedQuestion suggestedQuestion = new SuggestedQuestion(questionStr, SuggestedQuestion.QuestionFormat.WEB_TEMPLATE);
                suggestedQuestionList.add(suggestedQuestion);
                msg = "您可以试着问我：";
            }

            // 保存出记录，返回数据
            chatMemory.add(conversationId, new AssistantMessage(msg));
            return Flux.just(AiChatResponse.createResponse(msg, conversationId, new AiChatResponseMeta(suggestedQuestionList)));
        }

        // 获取对话用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        FmContentInfo fmContentInfo = contentInfo.getFmContentInfo();

        // 2.基于知识库回答问题
        var b = new FilterExpressionBuilder();
        var exp = b.and(
                b.eq("appId", contentInfo.getAppId()),
                b.or(b.eq("tenantId", contentInfo.getTenantId()), b.eq("tenantId", "common"))
        ).build();
        var qaAdvisor = QuestionAnswerAdvisor.builder(vectorStore)
                .searchRequest(SearchRequest.builder()
                        .similarityThreshold(0.6d)
                        .topK(5)
                        .filterExpression(exp)
                        .build())
                .build();
        ChatClient.ChatClientRequestSpec clientRequestSpec = defaultChatClient
                .prompt(PromptsUtil.getPrompts("fm/mix-web-demo-out-knowledgeBaseQuery"))
                .user(message)
                .advisors(
                        MessageChatMemoryAdvisor.builder(chatMemory).conversationId(conversationId).build(),
                        qaAdvisor
                );
        Flux<AiChatResponse> llmFlux;
        if (isStream) {
            llmFlux = clientRequestSpec.stream().content().map(r -> {
                String msg = new String(r.getBytes(), StandardCharsets.UTF_8);
                return AiChatResponse.createResponse(msg, conversationId);
            });
        } else {
            String msg = clientRequestSpec.call().content();
            llmFlux = Flux.just(AiChatResponse.createResponse(msg, conversationId));
        }

        // 3.判断归属模块，增加跳转链接
        // 分类：R 创建需求 F 创建工单 C 需求查询 S 工单查询 W 待处理工单查询 Q 待处理需求查询 I 巡检计划 T 巡检任务查询 O 其它问题
        List<Message> messages = chatMemory.get(conversationId);
        String module = intentClient.prompt()
                .user(u -> u.text(PromptsUtil.getPrompts("fm/mix-web-demo-out-intentionJudgment"))
                        .params(Map.of("inputText", message, "historyMessages", messages)))
                .call().content();
        // 处理跳转链接
        String moduleName = "";
        String moduleJumpUrl = "";
        if (StrUtil.isNotBlank(module) && !"O".equals(module)) {
            // 获取模块名称
            FmAppIntentRouterDTO fmAppIntentRouterDTO = getAppIntentRouterDTO(module, fmContentInfo.getTenantConfig());
            if (fmAppIntentRouterDTO != null) {
                moduleName = fmAppIntentRouterDTO.getName();
                moduleJumpUrl = fmAppIntentRouterDTO.getRouter();
            }
        }
        // 获取用户有权限的项目列表，拼接跳转按钮
        // 获取租户信息，判断跳转地址
        FmProperties.TenantConfig tenantConfig = fmContentInfo.getTenantConfig();
        String baseUrl = tenantConfig.getBaseUrl();
        String tenantId = contentInfo.getTenantId();

        Flux<AiChatResponse> jumpFlux = null;
        Flux<AiChatResponse> cutFlux = null;
        if (StrUtil.isNotBlank(moduleName)) {
            // 配置完成的租户读取FM数据进行跳转，其它使用默认的测试跳转
            if (!CommonConstant.DEFAULT_TENANT_ID.equals(tenantId)) {
                List<FmProjectDTO> projectList = fmContentInfo.getProjectList();
                if (projectList != null && !projectList.isEmpty()) {
                    cutFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.CUT)));
                    // 跳转链接需要处理登录问题
                    String msg = StrUtil.format("已为您找到{}功能，点击下方入口进入", moduleName);
                    FmWebSuggestionDTO fmWebSuggestionDTO = new FmWebSuggestionDTO();
                    List<FmWebSuggestionDTO.Option> optionList = new ArrayList<>(projectList.size());
                    for (FmProjectDTO fmProjectDTO : projectList) {
                        String toUrl = baseUrl + "/" + moduleJumpUrl.replace("[projectId]", fmProjectDTO.getId().toString());
                        String jumpUrl = StrUtil.format("{}/coe/demoLogin?toUrl={}", baseUrl, URLEncoder.encode(toUrl, StandardCharsets.UTF_8));
                        FmWebSuggestionDTO.Option option = new FmWebSuggestionDTO.Option("aiSmallEntry", fmProjectDTO.getName(), FmWebSuggestionDTO.ActionType.jump, jumpUrl);
                        optionList.add(option);
                    }
                    fmWebSuggestionDTO.setOptions(optionList);
                    SuggestedQuestion suggestedQuestion = new SuggestedQuestion(JSONUtil.toJsonStr(fmWebSuggestionDTO), SuggestedQuestion.QuestionFormat.WEB_TEMPLATE);
                    List<SuggestedQuestion> suggestedQuestionList = List.of(suggestedQuestion);
                    jumpFlux = Flux.just(AiChatResponse.createResponse(msg, conversationId, new AiChatResponseMeta(suggestedQuestionList)));
                }
            } else {
                // 未开启跳转功能支持的，使用默认测试跳转
                cutFlux = Flux.just(AiChatResponse.createResponse("", conversationId, new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.CUT)));
                String msg = StrUtil.format("已为您找到{}功能，点击下方入口进入", moduleName);
                SuggestedQuestion suggestedQuestion = new SuggestedQuestion(FmFlowConstant.DEMO_WEB_OUT_MODULE_JUMP_LINK_TEMPLATE, SuggestedQuestion.QuestionFormat.WEB_TEMPLATE);
                List<SuggestedQuestion> suggestedQuestionList = List.of(suggestedQuestion);
                jumpFlux = Flux.just(AiChatResponse.createResponse(msg, conversationId, new AiChatResponseMeta(suggestedQuestionList)));
            }
        }
        return jumpFlux == null ? llmFlux : Flux.concat(llmFlux, cutFlux, jumpFlux);
    }

    private static FmAppIntentRouterDTO getAppIntentRouterDTO(String module, FmProperties.TenantConfig tenantConfig) {
        if (StrUtil.isBlank(module)) {
            return null;
        }
        FmAppIntentRouterDTO fmAppIntentRouterDTO = null;
        if (FmProperties.TenantConfig.Type.SHANG.toString().equals(tenantConfig.getType())
                || FmProperties.TenantConfig.Type.FM2_0.toString().equals(tenantConfig.getType())) {
            fmAppIntentRouterDTO = FmAppIntentRouterDTO.indexByIntent(module, FmFlowConstant.DEMO_WEB_OUT_FM_INTENT_ROUTER_SET);
            // 老shang需要带上accessToken，FM2.0不需要
            if (FmProperties.TenantConfig.Type.FM2_0.toString().equals(tenantConfig.getType()) && fmAppIntentRouterDTO != null) {
                String router = fmAppIntentRouterDTO.getRouter();
                fmAppIntentRouterDTO.setRouter(router.replace("&access_token=[accessToken]", ""));
            }
        }
        return fmAppIntentRouterDTO;
    }

    public void initFmContentInfo(ContentInfo contentInfo) {
        FmContentInfo fmContentInfo = new FmContentInfo();
        // 初始化租户信息
        FmProperties.TenantConfig tenantConfig;
        if (StrUtil.isNotBlank(contentInfo.getTenantId()) && !CommonConstant.DEFAULT_TENANT_ID.equals(contentInfo.getTenantId())) {
            tenantConfig = fmProperties.getBase().getMultiTenant().getTenantConfig(contentInfo.getTenantId());
        } else {
            tenantConfig = fmProperties.getBase().getMultiTenant().getDefaultConfig();
        }
        // 租户不存在则使用默认租户
        if (tenantConfig == null) {
            log.error("租户{}不存在，使用默认租户", contentInfo.getTenantId());
            tenantConfig = fmProperties.getBase().getMultiTenant().getDefaultConfig();
        }
        fmContentInfo.setTenantConfig(tenantConfig);
        // 处理fm用户信息逻辑
        if (contentInfo.getAppCode().contains("wechat")) {
            // 微信端
            String[] split = contentInfo.getUsername().split("#_#");
            FmWechatUserInfoDTO wechatUserInfo;
            try {
                wechatUserInfo = fmBaseTool.getTenantWechatUserInfo(split[1], split[0], tenantConfig);
            } catch (Exception e) {
                log.error("用户信息获取失败", e);
                return;
            }
            if (wechatUserInfo == null) {
                log.error("用户不存在");
                return;
            }
            // 获取项目列表
            Long projectId = wechatUserInfo.getProject();
            if (projectId != null) {
                List<FmProjectDTO> projectDTOList = fmBaseTool.getTenantProjects(projectId, tenantConfig);
                List<FmProjectDTO> projectDTOS = null;
                if (projectDTOList != null && !projectDTOList.isEmpty()) {
                    fmContentInfo.setProjectList(projectDTOList);
                    projectDTOS = projectDTOList.stream().filter(f -> f.getId().equals(projectId)).toList();
                }
                if (projectDTOS != null && !projectDTOS.isEmpty()) {
                    FmProjectDTO currentProject = projectDTOS.getFirst();
                    if (currentProject != null) {
                        fmContentInfo.setFmProjectId(currentProject.getId());
                        fmContentInfo.setFmProjectName(currentProject.getName());
                    }
                }
            }
            fmContentInfo.setFmNickName(wechatUserInfo.getNickName());
            fmContentInfo.setFmRealName(wechatUserInfo.getName());
            fmContentInfo.setFmPhone(wechatUserInfo.getPhone());
            fmContentInfo.setFmOpenId(wechatUserInfo.getOpenId());
            contentInfo.setFmContentInfo(fmContentInfo);
        } else {
            // 其它端
            FmUserDataDTO fmUserDataDTO;
            try {
                fmUserDataDTO = fmBaseTool.getTenantUserInfo(contentInfo, tenantConfig);
            } catch (Exception e) {
                log.error("用户信息获取失败", e);
                return;
            }
            if (fmUserDataDTO == null) {
                log.error("用户不存在");
                return;
            }
            fmContentInfo.setFmRealName(fmUserDataDTO.getRealName());
            fmContentInfo.setFmUserName(fmUserDataDTO.getUserName());
            fmContentInfo.setFmPhone(fmUserDataDTO.getPhone());
            fmContentInfo.setFmUserId(fmUserDataDTO.getUserId());
            if (fmUserDataDTO.getProjects() != null) {
                fmContentInfo.setProjectList(fmUserDataDTO.getProjects());
            }
            contentInfo.setFmContentInfo(fmContentInfo);
        }
    }

    private void initFmContentProject(ContentInfo contentInfo, Long projectId) {
        // 处理fm用户信息逻辑
        FmContentInfo fmContentInfo = contentInfo.getFmContentInfo();
        if (fmContentInfo == null) {
            return;
        }
        // 如果已有项目列表，则不再获取，直接使用
        List<FmProjectDTO> projectList = fmContentInfo.getProjectList();
        if (projectList != null && !projectList.isEmpty()) {
            if (projectId != null) {
                List<FmProjectDTO> projectDTOS = projectList.stream().filter(f -> f.getId().equals(projectId)).toList();
                if (!projectDTOS.isEmpty()) {
                    FmProjectDTO currentProject = projectDTOS.getFirst();
                    if (currentProject != null) {
                        fmContentInfo.setFmProjectId(currentProject.getId());
                        fmContentInfo.setFmProjectName(currentProject.getName());
                    }
                }
                return;
            } else if (projectList.size() == 1) {
                FmProjectDTO currentProject = projectList.getFirst();
                if (currentProject != null) {
                    fmContentInfo.setFmProjectId(currentProject.getId());
                    fmContentInfo.setFmProjectName(currentProject.getName());
                }
                return;
            }
        }
        // 否则从FM获取项目列表
        if (contentInfo.getAppCode().contains("wechat")) {
            // 微信端
            if (projectId != null) {
                List<FmProjectDTO> projectDTOList = fmBaseTool.getTenantProjects(projectId, fmContentInfo.getTenantConfig());
                List<FmProjectDTO> projectDTOS = null;
                if (projectDTOList != null && !projectDTOList.isEmpty()) {
                    projectDTOS = projectDTOList.stream().filter(f -> f.getId().equals(projectId)).toList();
                }
                if (projectDTOS != null && !projectDTOS.isEmpty()) {
                    FmProjectDTO currentProject = projectDTOS.getFirst();
                    if (currentProject != null) {
                        fmContentInfo.setFmProjectId(currentProject.getId());
                        fmContentInfo.setFmProjectName(currentProject.getName());
                    }
                }
            }
        } else {
            // 其它端
            List<FmProjectDTO> projectDTOList = fmBaseTool.getTenantProjects(projectId, fmContentInfo.getTenantConfig());
            List<FmProjectDTO> projectDTOS;
            if (projectDTOList != null && !projectDTOList.isEmpty()) {
                fmContentInfo.setProjectList(projectDTOList);
                projectDTOS = projectDTOList.stream().filter(f -> f.getId().equals(projectId)).toList();
                if (!projectDTOS.isEmpty()) {
                    FmProjectDTO currentProject = projectDTOS.getFirst();
                    if (currentProject != null) {
                        fmContentInfo.setFmProjectId(currentProject.getId());
                        fmContentInfo.setFmProjectName(currentProject.getName());
                    }
                }
            } else {
                fmContentInfo.setProjectList(Lists.newArrayList());
            }
        }
    }

}
