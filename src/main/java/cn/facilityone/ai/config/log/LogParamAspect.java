package cn.facilityone.ai.config.log;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class LogParamAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogParamAspect.class);

    @Around("@annotation(logParam)")
    public Object around(ProceedingJoinPoint point, LogParam logParam) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        String methodName = signature.getDeclaringType().getSimpleName() + "." + signature.getName();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = point.getArgs();

        StringBuilder params = new StringBuilder();
        int paramCount = Math.min(parameterNames.length, args.length);
        for (int i = 0; i < paramCount; i++) {
            params.append(parameterNames[i])
                    .append("=")
                    .append(args[i] == null ? "null" : args[i].toString());
            if (i != paramCount - 1) {
                params.append(", ");
            }
        }
        logger.info("{}被调用,名称:{},参数:{}.", logParam.tag().getTagName(), methodName, params);

        Object result = point.proceed();

        ObjectMapper objectMapper = new ObjectMapper();
        logger.info("{}调用完成,名称:{},返回值:{}.", logParam.tag().getTagName(), methodName, logParam.printReturn() ? objectMapper.writeValueAsString(result) : "略");

        return result;
    }
}