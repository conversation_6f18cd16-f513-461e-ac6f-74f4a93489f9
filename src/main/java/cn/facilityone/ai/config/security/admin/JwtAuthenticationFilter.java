package cn.facilityone.ai.config.security.admin;

import cn.facilityone.ai.entity.RestResult;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;

import java.io.IOException;
import java.util.Collections;

@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final UserDetailsService userDetailsService;
    private final HandlerMapping handlerMapping;

    public JwtAuthenticationFilter(JwtUtils jwtUtils, UserDetailsService userDetailsService, HandlerMapping handlerMapping) {
        this.jwtUtils = jwtUtils;
        this.userDetailsService = userDetailsService;
        this.handlerMapping = handlerMapping;
    }

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                                    @NotNull FilterChain filterChain) throws ServletException, IOException {

        // 检查是否为admin请求
        if (!request.getServletPath().startsWith("/admin")) {
            filterChain.doFilter(request, response);
            return;
        }

        // 检查是否有SkipAuth注解
        if (hasSkipAuthAnnotation(request)) {
            log.info("跳过JWT认证，方法标注了@SkipAuth: {}", request.getRequestURI());
            filterChain.doFilter(request, response);
            return;
        }

        log.debug("处理管理员请求的JWT认证: {} {}",
                request.getMethod(), request.getRequestURI());

        String token = extractTokenFromRequest(request);
        if (token != null && jwtUtils.validateToken(token)) {
            String username = jwtUtils.getUsernameFromToken(token);
            log.debug("发现有效的管理员JWT令牌，用户: {}", username);

            UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    userDetails, null, userDetails.getAuthorities());
            authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authToken);

            log.debug("管理员认证成功，用户: {}", username);
        } else {
            log.warn("管理员请求中未找到有效的JWT令牌");
            // 直接返回错误响应，不继续执行过滤器链
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json;charset=UTF-8");

            RestResult<Void> errorApiResult = RestResult.error(RestResult.Status.UNAUTHORIZED.getCode(), "认证失败：未找到有效的JWT令牌");
            response.getWriter().write(JSONUtil.toJsonStr(errorApiResult));
            return;
        }

        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    private boolean hasSkipAuthAnnotation(HttpServletRequest request) {
        try {
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain != null && handlerChain.getHandler() instanceof HandlerMethod handlerMethod) {

                if (handlerMethod.hasMethodAnnotation(SkipAuth.class) || 
                    handlerMethod.getBeanType().isAnnotationPresent(SkipAuth.class)) {
                    // 设置匿名认证，但不应授予管理员权限
                    SecurityContextHolder.getContext().setAuthentication(
                        new UsernamePasswordAuthenticationToken("anonymous", null, 
                            Collections.singletonList(new SimpleGrantedAuthority("ROLE_ANONYMOUS")))
                    );
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("检查SkipAuth注解时发生异常: {}", e.getMessage());
        }
        return false;
    }
}
