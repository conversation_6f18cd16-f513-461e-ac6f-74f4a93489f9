package cn.facilityone.ai.config.security.openapi;

import cn.facilityone.ai.entity.RestResult;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
public class ApiAuthenticationFilter extends OncePerRequestFilter {

    private final AuthenticationManager authenticationManager;
    private final ApiAuthenticationConverter authenticationConverter;

    public ApiAuthenticationFilter(AuthenticationManager authenticationManager,
                                   ApiAuthenticationConverter authenticationConverter) {
        this.authenticationManager = authenticationManager;
        this.authenticationConverter = authenticationConverter;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, @NotNull HttpServletResponse response,
                                    @NotNull FilterChain filterChain) throws ServletException, IOException {

        // 跳过管理员路径
        if (request.getRequestURI().startsWith("/admin/")) {
            filterChain.doFilter(request, response);
            return;
        }

        // 跳过文件下载临时路径
        if (request.getRequestURI().startsWith("/file/download/temp/")) {
            filterChain.doFilter(request, response);
            return;
        }

        log.debug("Processing authentication for request: {} {}", request.getMethod(), request.getRequestURI());

        try {
            Authentication authentication = authenticationConverter.convert(request);
            if (authentication == null) {
                log.warn("请求中未找到认证令牌 {} {}", request.getMethod(), request.getRequestURI());
                // 未找到认证令牌时，也返回错误响应
                sendAuthenticationErrorResponse(response, "未提供有效的认证令牌");
                return;
            }

            log.debug("尝试为用户进行令牌认证: {}",
                    ((ApiAuthenticationToken) authentication).getUser());

            Authentication result = authenticationManager.authenticate(authentication);
            SecurityContextHolder.getContext().setAuthentication(result);

            log.info("用户认证成功: {}", result.getName());
        } catch (AuthenticationException e) {
            log.warn("请求认证失败 {} {}: {}",
                    request.getMethod(), request.getRequestURI(), e.getMessage());
            SecurityContextHolder.clearContext();
            // 直接返回错误响应，不继续执行过滤器链
            sendAuthenticationErrorResponse(response, e.getMessage());
            return;
        }

        filterChain.doFilter(request, response);
    }
    
    /**
     * 发送认证错误响应
     * 
     * @param response HTTP响应对象
     * @param errorMessage 错误信息
     * @throws IOException 如果写入响应时发生IO异常
     */
    private void sendAuthenticationErrorResponse(HttpServletResponse response, String errorMessage) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        RestResult<Void> errorApiResult = RestResult.error(RestResult.Status.UNAUTHORIZED.getCode(), "认证失败：" + errorMessage);
        response.getWriter().write(JSONUtil.toJsonStr(errorApiResult));
    }
}
