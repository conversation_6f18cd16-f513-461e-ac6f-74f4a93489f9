package cn.facilityone.ai.config.security;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
public class SecurityProperties {

    private Jwt jwt = new Jwt();
    private Api api = new Api();

    @Data
    public static class Jwt {
        private String header = "Authorization";
        private String prefix = "Bearer ";
    }

    @Data
    public static class Api {
        private Token token = new Token();
        private Whitelist whitelist = new Whitelist();

        @Data
        public static class Token {
            private String header = "Authorization";
            private String prefix = "Bearer ";
            private String userHeader = "X-User";
            private String userHeaderOld = "User";
            private String tenantHeader = "X-Tenant-Id";
        }

        @Data
        public static class Whitelist {
            private List<String> paths = List.of("/admin/**", "/error");
        }
    }

}