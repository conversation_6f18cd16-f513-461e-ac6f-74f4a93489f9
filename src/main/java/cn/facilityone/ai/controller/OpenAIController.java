package cn.facilityone.ai.controller;

import cn.facilityone.ai.service.biz.ReportAnalysisService;
import cn.facilityone.ai.service.tool.WeatherToolService;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.json.JSONUtil;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 11:25
 */


@RestController
public class OpenAIController {

    private final WeatherToolService weatherToolService;
    private final ChatClient chatClient;
    private final OpenAiChatModel chatModel;

    public OpenAIController(ChatClient chatClient, OpenAiChatModel chatModel, WeatherToolService weatherToolService) {
        this.chatClient = chatClient;
        this.chatModel = chatModel;
        this.weatherToolService = weatherToolService;
    }

    @Autowired
    private ReportAnalysisService reportAnalysisService;

    // 原始同步接口：一次性返回完整回复
    @GetMapping("/ai")
    public String ollama(@RequestParam String msg) {
        String content = chatClient.prompt().user(msg).tools(weatherToolService).call().content();
        System.out.println(content);
        return content;
    }

    @GetMapping(value = "/ai/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamResponse(@RequestParam String msg) {
        Flux<String> content = chatClient.prompt().user(msg).tools(weatherToolService).stream().content();
        System.out.println(content);
        return content
                .map(response -> new String(response.getBytes(), StandardCharsets.UTF_8));
    }

    /**
     * 格式化日期字符串为"dd/MM/yy"格式
     *
     * @param dateStr 原始日期字符串
     * @return 格式化后的日期字符串
     */
    private String formatDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试识别常见的日期格式
            String[] formats = {
                    "yyyy-MM-dd", "yyyy/MM/dd", "dd-MM-yyyy", "dd/MM/yyyy",
                    "MM-dd-yyyy", "MM/dd/yyyy", "yyyyMMdd", "yyyy.MM.dd",
                    "dd.MM.yyyy", "yyyy年MM月dd日"
            };

            Date date = null;
            for (String format : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false);
                    date = sdf.parse(dateStr);
                    if (date != null) {
                        break;
                    }
                } catch (ParseException e) {
                    // 尝试下一种格式
                }
            }

            if (date != null) {
                SimpleDateFormat targetFormat = new SimpleDateFormat("dd/MM/yy");
                return targetFormat.format(date);
            }

            // 如果无法识别，返回原始字符串
            return dateStr;
        } catch (Exception e) {
            System.err.println("日期格式化失败: " + dateStr + ", " + e.getMessage());
            return dateStr;
        }
    }

    /**
     * 格式化时间字符串为"HH:mm:ss"格式
     *
     * @param timeStr 原始时间字符串
     * @return 格式化后的时间字符串
     */
    private String formatTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试识别常见的时间格式
            String[] formats = {
                    "HH:mm:ss", "HH:mm", "HHmmss", "HHmm",
                    "hh:mm:ss a", "hh:mm a", "HH时mm分ss秒", "HH时mm分"
            };

            Date time = null;
            for (String format : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false);
                    time = sdf.parse(timeStr);
                    if (time != null) {
                        break;
                    }
                } catch (ParseException e) {
                    // 尝试下一种格式
                }
            }

            if (time != null) {
                SimpleDateFormat targetFormat = new SimpleDateFormat("HH:mm:ss");
                return targetFormat.format(time);
            }

            // 如果无法识别，但包含冒号，可能已经是合适的格式
            if (timeStr.contains(":")) {
                // 确保有秒数
                if (timeStr.split(":").length == 2) {
                    return timeStr + ":00";
                }
                return timeStr;
            }

            // 如果无法识别，返回原始字符串
            return timeStr;
        } catch (Exception e) {
            System.err.println("时间格式化失败: " + timeStr + ", " + e.getMessage());
            return timeStr;
        }
    }

    // 测试接口
    @GetMapping("/test")
    public String index() throws IOException {
        CsvData csvData = CsvUtil.getReader().read(new File("/Users/<USER>/Downloads/工单列表（中关村壹号-仲量） (3).csv"));
        // csv数据转换为WorkOrderData对象
        List<ReportAnalysisService.WorkOrderData> workOrderData = new ArrayList<>();

        // 获取所有行
        List<CsvRow> rows = csvData.getRows();
        // 获取表头信息
        CsvRow headerRow = rows.getFirst();
        List<String> headerList = headerRow.getRawList();

        // 跳过表头行
        for (int i = 1; i < rows.size(); i++) {
            CsvRow row = rows.get(i);
            List<String> values = row.getRawList();

            try {
                ReportAnalysisService.WorkOrderData workOrder = new ReportAnalysisService.WorkOrderData();

                // 根据提供的表头信息进行映射
                // "﻿工单号", "申请人", "联系电话", "创建日期", "创建时间", "发布人", "发布日期", "发布时间", "执行人", "接单日期", "接单时间"...
                if (values.size() > 0) workOrder.setOrderNumber(values.get(0));
                if (values.size() > 1) workOrder.setApplicant(values.get(1));
                // 联系电话不在WorkOrderData中，跳过

                // 处理日期时间格式，确保符合"dd/MM/yy"格式
                if (values.size() > 3) {
                    String createDate = formatDate(values.get(3));
                    workOrder.setCreateDate(createDate);
                }
                if (values.size() > 4) {
                    String createTime = formatTime(values.get(4));
                    workOrder.setCreateTime(createTime);
                }
                if (values.size() > 5) workOrder.setPublisher(values.get(5));
                if (values.size() > 6) {
                    String publishDate = formatDate(values.get(6));
                    workOrder.setPublishDate(publishDate);
                }
                if (values.size() > 7) {
                    String publishTime = formatTime(values.get(7));
                    workOrder.setPublishTime(publishTime);
                }
                if (values.size() > 8) workOrder.setExecutor(values.get(8));
                if (values.size() > 9) {
                    String acceptDate = formatDate(values.get(9));
                    workOrder.setAcceptDate(acceptDate);
                }
                if (values.size() > 10) {
                    String acceptTime = formatTime(values.get(10));
                    workOrder.setAcceptTime(acceptTime);
                }

                // 服务类型有多个字段，合并处理 (索引11-14)
                StringBuilder serviceType = new StringBuilder();
                for (int j = 11; j <= 14; j++) {
                    if (values.size() > j && values.get(j) != null && !values.get(j).trim().isEmpty()) {
                        if (serviceType.length() > 0) {
                            serviceType.append(" / ");
                        }
                        serviceType.append(values.get(j).trim());
                    }
                }
                workOrder.setServiceType(serviceType.toString());

                if (values.size() > 15) workOrder.setPriority(values.get(15));

                // 部门有多个字段，合并处理 (索引16-18)
                StringBuilder department = new StringBuilder();
                for (int j = 16; j <= 18; j++) {
                    if (values.size() > j && values.get(j) != null && !values.get(j).trim().isEmpty()) {
                        if (department.length() > 0) {
                            department.append(" / ");
                        }
                        department.append(values.get(j).trim());
                    }
                }
                workOrder.setDepartment(department.toString());

                // 项目不在WorkOrderData中，跳过
                if (values.size() > 20) workOrder.setBuilding(values.get(20));
                if (values.size() > 21) workOrder.setFloor(values.get(21));
                if (values.size() > 22) workOrder.setRoom(values.get(22));
                if (values.size() > 23) workOrder.setFullLocation(values.get(23));
                if (values.size() > 24) workOrder.setDescription(values.get(24));
                // 缴费项不在WorkOrderData中，跳过
                if (values.size() > 26) workOrder.setOrderStatus(values.get(26));
                if (values.size() > 27) workOrder.setPauseReason(values.get(27));
                if (values.size() > 28) workOrder.setChargeable(values.get(28));
                // 收费金额不在WorkOrderData中，跳过
                if (values.size() > 30) {
                    String estimatedStartDate = formatDate(values.get(30));
                    workOrder.setEstimatedStartDate(estimatedStartDate);
                }
                if (values.size() > 31) {
                    String estimatedStartTime = formatTime(values.get(31));
                    workOrder.setEstimatedStartTime(estimatedStartTime);
                }
                if (values.size() > 32) {
                    String estimatedEndDate = formatDate(values.get(32));
                    workOrder.setEstimatedEndDate(estimatedEndDate);
                }
                if (values.size() > 33) {
                    String estimatedEndTime = formatTime(values.get(33));
                    workOrder.setEstimatedEndTime(estimatedEndTime);
                }
                if (values.size() > 34) workOrder.setEstimatedWorkDuration(values.get(34));
                if (values.size() > 35) {
                    String actualStartDate = formatDate(values.get(35));
                    workOrder.setActualStartDate(actualStartDate);
                }
                if (values.size() > 36) {
                    String actualStartTime = formatTime(values.get(36));
                    workOrder.setActualStartTime(actualStartTime);
                }
                if (values.size() > 37) {
                    String actualEndDate = formatDate(values.get(37));
                    workOrder.setActualEndDate(actualEndDate);
                }
                if (values.size() > 38) {
                    String actualEndTime = formatTime(values.get(38));
                    workOrder.setActualEndTime(actualEndTime);
                }
                if (values.size() > 39) workOrder.setActualWorkDuration(values.get(39));
                if (values.size() > 40) workOrder.setOrderPauseDuration(values.get(40));
                if (values.size() > 41) workOrder.setWorkContent(values.get(41));
                // 需求编号、服务质量、服务态度、服务速度、满意度评价内容不在WorkOrderData中，跳过

                workOrderData.add(workOrder);
            } catch (Exception e) {
                System.err.println("解析第" + i + "行数据失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        ReportAnalysisService.WorkOrderAnalysisResult result = reportAnalysisService.analyzeWorkOrders(workOrderData);
        System.out.println("工单总数: " + result.getTotalCount());
        System.out.println("工单状态分布: " + result.getStatusDistribution());
        System.out.println("工单优先级分布: " + result.getPriorityDistribution());
        System.out.println("工单服务类型分布: " + result.getServiceTypeDistribution());
        System.out.println("工单部门分布: " + result.getDepartmentDistribution());
        System.out.println("工单位置分布: " + result.getLocationDistribution());
        System.out.println("工单月份分布: " + result.getMonthlyDistribution());
        System.out.println("工单星期分布: " + result.getWeekdayDistribution());
        System.out.println("工单时间分布: " + result.getHourlyDistribution());
        System.out.println("工单处理时长: " + result.getAvgProcessingTimeMinutes());
        System.out.println("工单完成率: " + result.getCompletionRate());
        System.out.println("工单执行者分布: " + result.getExecutorDistribution());
        System.out.println("工单收费分布: " + result.getChargeableDistribution());
        String s = reportAnalysisService.analyzeByChatGPT(JSONUtil.toJsonStr(result));
        System.out.println("AI分析结果：" + s);
        return s;
    }
}