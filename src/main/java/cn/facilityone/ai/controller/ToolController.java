package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.UploadedImage2TextDTO;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.biz.FileBizService;
import cn.facilityone.ai.service.biz.ToolBizService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:16
 */


@RestController
@RequestMapping("tool")
public class ToolController {


    private final ToolBizService toolBizService;
    private final FileBizService fileBizService;

    public ToolController(ToolBizService toolBizService, FileBizService fileBizService) {
        this.toolBizService = toolBizService;
        this.fileBizService = fileBizService;
    }

    @PostMapping(value = "/audio2Text")
    public RestResult<String> audio2Text(@RequestParam("file") MultipartFile file) {
        FileEntity fileEntity = fileBizService.uploadFile(file);
        String text = toolBizService.audio2Text(fileEntity);
        return new RestResult<>(RestResult.Status.SUCCESS, text);
    }

    /**
     * 图片转文字（支持多图）
     *
     * @param files 多图片文件
     * @param type  操作类型：repair-create:工单需求单创建;repair-complete:工单需求单完成。
     * @return 文本内容
     */
    @PostMapping(value = "/image2Text")
    public RestResult<String> multipleImages2Text(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        List<FileEntity> fileEntityList = fileBizService.uploadMultipleFile(files);
        String text = toolBizService.multipleImages2Text(fileEntityList, type);
        return new RestResult<>(RestResult.Status.SUCCESS, text);
    }

    /**
     * 图片转文字（支持多图）
     *
     * @return 文本内容
     */
    @PostMapping(value = "/uploadedImage2Text")
    public RestResult<String> uploadedImage2Text(@RequestBody UploadedImage2TextDTO paramDTO) {
        String text = toolBizService.uploadedImage2Text(paramDTO.getFileIds(), paramDTO.getType());
        return new RestResult<>(RestResult.Status.SUCCESS, text);
    }

}
