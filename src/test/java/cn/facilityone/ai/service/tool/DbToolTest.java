package cn.facilityone.ai.service.tool;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;
import java.util.Arrays;
import java.util.List;

/**
 * DbTool 测试类
 * 主要测试参数化查询功能和SQL注入防护
 */
class DbToolTest {

    private DbTool dbTool;

    @BeforeEach
    void setUp() {
        dbTool = new DbTool();
    }

    @Test
    void testParameterizedRequestCreation() {
        // 测试参数化请求对象创建
        List<Object> parameters = Arrays.asList("active", 18, "admin");
        
        DbTool.ParameterizedRequest request = new DbTool.ParameterizedRequest(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM users WHERE status = ? AND age > ? AND role = ?",
            parameters,
            100
        );

        assertNotNull(request);
        assertEquals("***********************************", request.url());
        assertEquals("root", request.username());
        assertEquals("password", request.password());
        assertEquals("SELECT * FROM users WHERE status = ? AND age > ? AND role = ?", request.sql());
        assertEquals(3, request.parameters().size());
        assertEquals("active", request.parameters().get(0));
        assertEquals(18, request.parameters().get(1));
        assertEquals("admin", request.parameters().get(2));
        assertEquals(100, request.limit());
    }

    @Test
    void testRequestWithParameters() {
        // 测试普通Request对象包含参数
        List<Object> parameters = Arrays.asList("test", 123);
        
        DbTool.Request request = new DbTool.Request(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM table WHERE name = ? AND id = ?",
            50,
            parameters
        );

        assertNotNull(request);
        assertEquals(2, request.parameters().size());
        assertEquals("test", request.parameters().get(0));
        assertEquals(123, request.parameters().get(1));
    }

    @Test
    void testSqlValidation() {
        // 测试SQL语句验证
        // 这里可以添加更多的单元测试来验证SQL安全检查逻辑
        
        // 测试正常的SELECT语句
        String validSql = "SELECT * FROM users WHERE id = ?";
        assertTrue(validSql.toUpperCase().trim().startsWith("SELECT"));
        
        // 测试危险的SQL语句
        String dangerousSql = "DROP TABLE users";
        assertTrue(dangerousSql.toUpperCase().contains("DROP"));
    }

    @Test
    void testParameterPlaceholderCount() {
        // 测试参数占位符数量计算
        String sql1 = "SELECT * FROM users WHERE id = ?";
        long count1 = sql1.chars().filter(ch -> ch == '?').count();
        assertEquals(1, count1);
        
        String sql2 = "SELECT * FROM users WHERE name = ? AND age > ? AND status = ?";
        long count2 = sql2.chars().filter(ch -> ch == '?').count();
        assertEquals(3, count2);
        
        String sql3 = "SELECT * FROM users";
        long count3 = sql3.chars().filter(ch -> ch == '?').count();
        assertEquals(0, count3);
    }

    @Test
    void testParameterTypes() {
        // 测试不同类型的参数
        List<Object> mixedParameters = Arrays.asList(
            "string_value",      // String
            42,                  // Integer
            3.14,               // Double
            true,               // Boolean
            null                // Null
        );
        
        assertEquals(5, mixedParameters.size());
        assertTrue(mixedParameters.get(0) instanceof String);
        assertTrue(mixedParameters.get(1) instanceof Integer);
        assertTrue(mixedParameters.get(2) instanceof Double);
        assertTrue(mixedParameters.get(3) instanceof Boolean);
        assertNull(mixedParameters.get(4));
    }
}
