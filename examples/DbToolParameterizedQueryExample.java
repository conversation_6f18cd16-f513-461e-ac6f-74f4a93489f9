package examples;

import cn.facilityone.ai.service.tool.DbTool;
import java.util.Arrays;
import java.util.List;

/**
 * DbTool 参数化查询使用示例
 * 演示如何安全地执行数据库查询，避免SQL注入风险
 */
public class DbToolParameterizedQueryExample {

    public static void main(String[] args) {
        DbTool dbTool = new DbTool();
        
        // 示例1：使用原有Request对象进行参数化查询
        System.out.println("=== 示例1：使用Request对象进行参数化查询 ===");
        
        List<Object> parameters1 = Arrays.asList("active", 25, "IT");
        DbTool.Request request1 = new DbTool.Request(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM users WHERE status = ? AND age > ? AND department = ?",
            50,
            parameters1
        );
        
        // 注意：这里只是演示，实际使用时需要有真实的数据库连接
        // DbTool.Response response1 = dbTool.apply(request1);
        System.out.println("SQL: " + request1.sql());
        System.out.println("参数: " + request1.parameters());
        
        // 示例2：使用新的ParameterizedRequest对象
        System.out.println("\n=== 示例2：使用ParameterizedRequest对象 ===");
        
        List<Object> parameters2 = Arrays.asList("John", "Doe", true);
        DbTool.ParameterizedRequest request2 = new DbTool.ParameterizedRequest(
            "***********************************",
            "root",
            "password",
            "SELECT id, email FROM users WHERE first_name = ? AND last_name = ? AND active = ?",
            parameters2,
            100
        );
        
        // DbTool.Response response2 = dbTool.executeParameterized(request2);
        System.out.println("SQL: " + request2.sql());
        System.out.println("参数: " + request2.parameters());
        
        // 示例3：不同类型的参数
        System.out.println("\n=== 示例3：不同类型的参数 ===");
        
        List<Object> mixedParameters = Arrays.asList(
            "admin",           // String
            42,                // Integer
            3.14,              // Double
            true,              // Boolean
            null               // Null
        );
        
        DbTool.Request request3 = new DbTool.Request(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM users WHERE role = ? AND age > ? AND score > ? AND active = ? AND notes IS ?",
            20,
            mixedParameters
        );
        
        System.out.println("SQL: " + request3.sql());
        System.out.println("参数类型和值:");
        for (int i = 0; i < mixedParameters.size(); i++) {
            Object param = mixedParameters.get(i);
            String type = param != null ? param.getClass().getSimpleName() : "null";
            System.out.println("  参数" + (i + 1) + ": " + param + " (类型: " + type + ")");
        }
        
        // 示例4：安全性对比
        System.out.println("\n=== 示例4：安全性对比 ===");
        
        String userName = "admin'; DROP TABLE users; --";
        
        // ❌ 不安全的字符串拼接方式（容易受到SQL注入攻击）
        String unsafeSql = "SELECT * FROM users WHERE username = '" + userName + "'";
        System.out.println("不安全的SQL: " + unsafeSql);
        
        // ✅ 安全的参数化查询方式
        String safeSql = "SELECT * FROM users WHERE username = ?";
        List<Object> safeParameters = Arrays.asList(userName);
        System.out.println("安全的SQL: " + safeSql);
        System.out.println("安全的参数: " + safeParameters);
        
        // 示例5：复杂查询示例
        System.out.println("\n=== 示例5：复杂查询示例 ===");
        
        List<Object> complexParameters = Arrays.asList(
            "2023-01-01",      // 开始日期
            "2023-12-31",      // 结束日期
            Arrays.asList("IT", "HR", "Finance"),  // 部门列表（注意：这需要特殊处理）
            1000               // 最小薪资
        );
        
        // 注意：对于IN子句，需要动态构建占位符
        String inClause = "?,?,?"; // 对应3个部门
        String complexSql = String.format(
            "SELECT u.*, d.name as dept_name FROM users u " +
            "JOIN departments d ON u.dept_id = d.id " +
            "WHERE u.created_date BETWEEN ? AND ? " +
            "AND d.name IN (%s) " +
            "AND u.salary > ? " +
            "ORDER BY u.created_date DESC",
            inClause
        );
        
        // 展开部门列表参数
        List<Object> flatParameters = Arrays.asList(
            "2023-01-01", "2023-12-31", "IT", "HR", "Finance", 1000
        );
        
        System.out.println("复杂SQL: " + complexSql);
        System.out.println("展开后的参数: " + flatParameters);
        
        System.out.println("\n=== 总结 ===");
        System.out.println("1. 始终使用参数化查询，避免SQL注入风险");
        System.out.println("2. 确保参数数量与占位符(?)数量匹配");
        System.out.println("3. 支持多种参数类型：String, Integer, Long, Double, Float, Boolean, Date, null");
        System.out.println("4. 对于复杂的IN子句，需要动态构建占位符并展开参数列表");
        System.out.println("5. 参数化查询不仅更安全，还能提高性能（SQL预编译）");
    }
}
